#include "ble_manager.h"
#include <Arduino.h>
#include "motor_control.h"
#include "serial_command.h"

extern int stop_Bit;
extern MotorControl motor;
float motor_speed = 0;
float motor_steps = 0;
// 新增：第三种电机模式相关变量
uint8_t motor_mode = 0;        // 0=速度模式, 1=位置模式, 2=脉冲模式
uint32_t motor_pulse_target = 8388608;  // 脉冲模式目标位置
uint8_t motor_pulse_dir = 0;   // 脉冲模式方向
float motor_pulse_speed = 0;   // 脉冲模式速度
extern HardwareSerial MySerial_0;
// 回调类实现
void BLEServerCallbacksImpl::onConnect(BLEServer* pServer) {
    manager.setConnected(true);
    Serial.printf("BLE Device Connected");
}

void BLEServerCallbacksImpl::onDisconnect(BLEServer* pServer) {
    manager.setConnected(false);
    Serial.println("BLE Device Disconnected");
    pServer->startAdvertising();
}


void BLECharacteristicCallbacksImpl::onWrite(BLECharacteristic* pCharacteristic) {
    std::string value = pCharacteristic->getValue();

    if (value.length() < 5) {
        Serial.println("Received invalid or too short BLE command.");
        return;
    }


    GPIO.out_w1tc = (1 << 10); // 置高

    // Trim whitespace from value
    value.erase(value.find_last_not_of(" \n\r\t")+1);
    //Serial.printf("%s", value.c_str());
    try {
        if (value.find("Tup:") == 0 && value.length() > 4) {
            const char* speedStr = value.substr(4).c_str();
            float speed = atof(speedStr);
            //Serial.printf("Tup:%.8f\n", speed);
            MySerial_0.printf("Tup:%.8f\n", speed);
            MySerial_0.flush(); // 强制刷新发送缓冲区
        } 
        else if (value.find("Tdown:") == 0 && value.length() > 6) {
            const char* speedStr = value.substr(6).c_str();
            float speed = atof(speedStr);
            //Serial.printf("Tdown:%.8f\n", speed);
            MySerial_0.printf("Tdown:%.8f\n", speed);
            MySerial_0.flush(); // 强制刷新发送缓冲区
        }
        else if (value.find("Tleft:") == 0 && value.length() > 6) {
            const char* speedStr = value.substr(6).c_str();
            float speed = atof(speedStr);
            //Serial.printf("Tleft:%.8f\n", speed);
            motor_speed = -speed;
            motor_mode = 0;
        }
        else if (value.find("Tright:") == 0 && value.length() > 7) {
            const char* speedStr = value.substr(7).c_str();
            float speed = atof(speedStr);
            //Serial.printf("Tright:%.8f\n", speed);
            motor_speed = speed;
            motor_mode = 0;
        }
        else if (value == "stop_rl") {
            //Serial.printf("stop_rl\n");
            motor_speed = 0;
            motor_mode = 0;
        }
        else if (value == "stop_ud") {
            //Serial.printf("stop_ud\n");
            MySerial_0.println("stop_ud\n");
            MySerial_0.flush(); // 强制刷新发送缓冲区
        }
        else if (value.find("up:") == 0) {
            size_t colon_pos = value.find(":");
            size_t space_pos = value.find(" ");
            if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                float steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
                //Serial.printf("up:%02d speed:%.8f\n",steps,speed);
                MySerial_0.printf("up:%.8f speed:%.8f\n",steps,speed);
                MySerial_0.flush(); // 强制刷新发送缓冲区
            } else {
            }
        }
        else if (value.find("right:") == 0) {
            size_t colon_pos = value.find(":");
            size_t space_pos = value.find(" ");
            if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                float steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
                //Serial.printf("right:%02d speed:%.8f\n",steps,speed);
                motor_speed = speed;
                motor_steps = steps;
                motor_mode = 1;
            } else {
            }
        }
        else if (value.find("down:") == 0) {
            size_t colon_pos = value.find(":");
            size_t space_pos = value.find(" ");
            if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                float steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
                //Serial.printf("down:%02d speed:%.8f\n",steps,speed);
                MySerial_0.printf("down:%.8f speed:%.8f\n",steps,speed);
                MySerial_0.flush(); // 强制刷新发送缓冲区
            } else {
            }
        }
        else if (value.find("left:") == 0) {
            size_t colon_pos = value.find(":");
            size_t space_pos = value.find(" ");
            if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                float steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
                //Serial.printf("left:%02d speed:%.8f\n",steps,speed);
                motor_speed = -speed;
                motor_steps = steps;
                motor_mode = 1;
            } else {
            }
        }
        else if (value.find("de_pulse:") == 0) {
                    // 新增：脉冲模式命令 格式: pulse:目标位置 speed:速度 dir:方向
                    // 例如: pulse:8388000 speed:30.0 dir:1
                    size_t pulse_pos = value.find(":");
                    size_t speed_pos = value.find(" speed:");
                    size_t dir_pos = value.find(" dir:");

                    if (pulse_pos != std::string::npos && speed_pos != std::string::npos && dir_pos != std::string::npos) {
                        uint32_t target = (uint32_t)atol(value.substr(pulse_pos + 1, speed_pos - pulse_pos - 1).c_str());
                        float speed = atof(value.substr(speed_pos + 7, dir_pos - speed_pos - 7).c_str());
                        uint8_t dir = (uint8_t)atoi(value.substr(dir_pos + 5).c_str());

                        // 限制目标位置在有效范围内
                        if (target <= 16777215) {
                            motor_pulse_target = target;
                            motor_pulse_speed = speed;
                            motor_pulse_dir = dir;
                            motor_mode = 2; // 设置为脉冲模式
                        }
                    }
                }
        else if (value.find("ra_pulse:") == 0) {
                    // 新增：脉冲模式命令 格式: pulse:目标位置 speed:速度 dir:方向
                    // 例如: pulse:8388000 speed:30.0 dir:1
                    size_t pulse_pos = value.find(":");
                    size_t speed_pos = value.find(" speed:");
                    size_t dir_pos = value.find(" dir:");

                    if (pulse_pos != std::string::npos && speed_pos != std::string::npos && dir_pos != std::string::npos) {
                        uint32_t target = (uint32_t)atol(value.substr(pulse_pos + 1, speed_pos - pulse_pos - 1).c_str());
                        float speed = atof(value.substr(speed_pos + 7, dir_pos - speed_pos - 7).c_str());
                        uint8_t dir = (uint8_t)atoi(value.substr(dir_pos + 5).c_str());

                        // 限制目标位置在有效范围内
                        if (target <= 16777215) {
                            MySerial_0.printf("pulse:%u speed:%.8f dir:%u\n",target,speed,dir);
                            MySerial_0.flush(); // 强制刷新发送缓冲区
                        }
                    }
                }
         else {
            Serial.println("Unknown command received.");
        }
    } catch (...) {
        Serial.println("Exception while processing BLE command.");
    }
}

BLEManager& BLEManager::getInstance() {
    static BLEManager instance;
    return instance;
}

BLEManager::BLEManager() 
    : pServer(nullptr), pService(nullptr), pCharacteristic(nullptr), 
      deviceConnected(false),
      serverCallbacks(*this),
      charCallbacks(*this) {
}

void BLEManager::setConnected(bool connected) {
    deviceConnected = connected;
}

void BLEManager::init() {
    // 使用基于MAC地址的唯一设备名称
    String uniqueName = DeviceIdentifier::getUniqueDeviceName(BLE_DEVICE_BASE_NAME);
    BLEDevice::init(uniqueName.c_str());
    setupBLEServer();
    setupBLEService();
    setupBLECharacteristic();
}

void BLEManager::start() {
    pService->start();
    
    BLEAdvertising* pAdvertising = BLEDevice::getAdvertising();
    pAdvertising->addServiceUUID(SERVICE_UUID);
    pAdvertising->setScanResponse(true);
    pAdvertising->start();
}

void BLEManager::stop() {
    //Serial.println("Stop bit is false, stopping Bluetooth...");

    // 停止广播
    pServer->getAdvertising()->stop();

    // 完全反初始化蓝牙（之后蓝牙模块不可用）
    BLEDevice::deinit();  
    
    // 防止多次执行
    stop_Bit = -1; // 或设置其他标志，防止重复执行
}

bool BLEManager::isConnected() const {
    return deviceConnected;
}

void BLEManager::sendNotification(const std::string& value) {
    if (deviceConnected && pCharacteristic) {
        pCharacteristic->setValue(value);
        pCharacteristic->notify();
    }
}

void BLEManager::setupBLEServer() {
    pServer = BLEDevice::createServer();
    pServer->setCallbacks(&serverCallbacks);
}

void BLEManager::setupBLEService() {
    pService = pServer->createService(SERVICE_UUID);
}

void BLEManager::setupBLECharacteristic() {
    pCharacteristic = pService->createCharacteristic(
        CHARACTERISTIC_UUID,
        NIMBLE_PROPERTY::READ |
        NIMBLE_PROPERTY::WRITE |
        NIMBLE_PROPERTY::NOTIFY
    );
    pCharacteristic->setCallbacks(&charCallbacks);
    pCharacteristic->setValue("Hello World");
}

void bleTask(void* parameter) {
    BLEManager& bleManager = BLEManager::getInstance();
    bleManager.init();
    bleManager.start();

    while (true) {
        // BLE 任务主循环
        if (stop_Bit == 0) {
            break;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    // 退出时关闭蓝牙
    bleManager.stop();
    vTaskDelete(NULL);  // 删除任务
}
