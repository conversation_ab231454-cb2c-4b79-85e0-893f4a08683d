#ifndef MOTOR_CONTROL_H
#define MOTOR_CONTROL_H

#include <Arduino.h>
#include "driver/ledc.h"
#include "pid_controller.h"
extern float curr_speed;
class Encoder;  // 前向声明

/**
 * @brief 电机控制类
 * @details 提供步进电机的速度控制和位置控制功能，支持两种控制模式：
 *          速度模式和位置模式，集成PID控制器实现精确控制
 */
class MotorControl {
public:
    /**
     * @brief 控制模式枚举
     */
    enum ControlMode {
        SPEED_MODE,     ///< 速度控制模式
        POSITION_MODE,  ///< 位置控制模式
        PULSE_MODE      ///< 虚拟编码器脉冲模式
    };

    /**
     * @brief 初始化电机控制系统
     * @details 配置GPIO引脚、硬件定时器、编码器和PID控制器
     */
    void init();

    /**
     * @brief 设置电机输出
     * @param output 输出值（速度或PWM占空比）
     */
    void setOutput(float output);

    /**
     * @brief 设置目标位置
     * @param target 目标位置值
     */
    void setPosition(float target);

    /**
     * @brief 设置控制模式
     * @param mode 控制模式（速度模式或位置模式）
     */
    void setControlMode(ControlMode mode);

    /**
     * @brief 更新位置控制
     * @details 在位置控制模式下调用，执行PID控制算法
     */
    void updatePositionControl();

    /**
     * @brief 获取当前位置
     * @return 当前位置值
     */
    float getCurrentPosition() const;

    /**
     * @brief 获取目标位置
     * @return 目标位置值
     */
    float getTargetPosition() const;

    /**
     * @brief 设置为位置控制模式
     */
    void setpositionmode();

    /**
     * @brief 设置为速度控制模式
     */
    void setspeedmode();

    /**
     * @brief 设置为虚拟编码器脉冲模式
     */
    void setpulsemode();

    /**
     * @brief 设置虚拟编码器目标位置
     * @param target 目标脉冲计数值（0~16777215）
     * @param speed 移动速度
     * @param dir 方向（0或1）
     */
    void setPulseTarget(uint32_t target, float speed, uint8_t dir);

    /**
     * @brief 更新虚拟编码器脉冲控制
     * @details 在脉冲模式下调用，执行基于虚拟编码器的位置控制
     */
    void updatePulseControl();

    /**
     * @brief 获取当前虚拟编码器计数值
     * @return 当前虚拟编码器计数值
     */
    uint32_t getCurrentPulseCount() const;

    /**
     * @brief 重置虚拟编码器计数器
     * @param value 新的计数值，默认为8388608（中心位置）
     */
    void resetPulseCount(uint32_t value = 8388608);

    /**
     * @brief 重置脉冲控制状态
     * @details 重置内部静态变量，防止状态残留
     */
    void resetPulseControlState();

    /**
     * @brief 获取脉冲模式调试信息
     * @param current 当前计数值
     * @param target 目标计数值
     * @param dir 方向参数
     * @param reached 是否到达目标
     */
    void getPulseDebugInfo(uint32_t* current, uint32_t* target, uint8_t* dir, bool* reached) const;

    /**
     * @brief 紧急停止
     * @details 立即停止电机运动
     */
    void emergencyStop();

    PIDController pidController = PIDController(3, 0.3, 0.4); ///< PID控制器实例

    // 需要在中断中访问的成员变量
    ControlMode currentMode = SPEED_MODE;
    uint32_t virtualPulseCount = 8388608;  // 虚拟编码器计数器，初始值8388608
    uint8_t pulseDirection = 0;            // 脉冲模式方向（0或1）

private:
    static const int16_t sin_table[];
    static const uint8_t PWM_FREQ;
    static const ledc_timer_bit_t PWM_RESOLUTION;
    static const ledc_channel_t PWM_CHANNEL_A;
    static const ledc_channel_t PWM_CHANNEL_B;

    float targetPosition = 0;
    float currentSpeed = 0;
    float targetSpeed = 0;
    float maxAcceleration = 5000.0f; // steps/s^2
    unsigned long lastUpdateTime = 0;

    // 多圈位置跟踪
    int totalRevolutions = 0;       // 累计圈数
    float lastAngle = 0;            // 上次角度值(用于检测过零)
    Encoder* encoder = nullptr;

    // 虚拟编码器脉冲模式相关变量
    uint32_t targetPulseCount = 8388608;   // 目标脉冲计数值
    float pulseSpeed = 0;                  // 脉冲模式速度
    bool pulseTargetReached = false;       // 是否到达目标位置
    unsigned long pulseStopTime = 0;       // 停止时间戳，用于防摆动
    int consecutiveSmallMoves = 0;         // 连续小幅移动计数
    int32_t lastStepDiff = 0;              // 上次步数差值
};

#endif
