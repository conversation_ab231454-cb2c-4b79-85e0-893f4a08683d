# 虚拟编码器脉冲模式实现总结

## 实现概述

成功为ESP32步进电机控制系统添加了第三种电机运动模式：**虚拟编码器脉冲模式**。这种模式使用一个24位虚拟计数器来跟踪电机位置，提供了精确的步进控制。

## 核心功能

### 1. 虚拟编码器计数器
- **范围**: 0 ~ 16,777,215 (24位)
- **初始值**: 8,388,608 (中点位置)
- **更新机制**: 每个电机步进脉冲实时更新计数器

### 2. 方向控制逻辑
- **dir = 0**: 电机正向运动(motor_speed>0)时计数器递增，反向运动时计数器递减
- **dir = 1**: 电机正向运动(motor_speed>0)时计数器递减，反向运动时计数器递增

### 3. 位置控制算法
- 自动计算当前位置与目标位置的差值
- 根据差值和方向参数确定电机运动方向
- 到达目标位置时自动停止

## 代码修改详情

### 1. 头文件修改 (`include/motor_control.h`)

#### 新增控制模式
```cpp
enum ControlMode {
    SPEED_MODE,     // 速度控制模式
    POSITION_MODE,  // 位置控制模式
    PULSE_MODE      // 虚拟编码器脉冲模式 (新增)
};
```

#### 新增成员变量
```cpp
// 公共成员变量 (需要在中断中访问)
ControlMode currentMode = SPEED_MODE;
uint32_t virtualPulseCount = 8388608;  // 虚拟编码器计数器
uint8_t pulseDirection = 0;            // 脉冲模式方向

// 私有成员变量
uint32_t targetPulseCount = 8388608;   // 目标脉冲计数值
float pulseSpeed = 0;                  // 脉冲模式速度
bool pulseTargetReached = false;       // 是否到达目标位置
```

#### 新增方法声明
```cpp
void setpulsemode();                                    // 设置脉冲模式
void setPulseTarget(uint32_t target, float speed, uint8_t dir); // 设置目标
void updatePulseControl();                             // 更新脉冲控制
uint32_t getCurrentPulseCount() const;                 // 获取当前计数
```

### 2. 实现文件修改 (`src/motor_control.cpp`)

#### 全局指针声明
```cpp
static MotorControl* g_motorControl = nullptr;
```

#### 定时器中断修改
在`onTimer()`中断函数中添加虚拟编码器更新逻辑：
```cpp
// 在脉冲模式下更新虚拟编码器计数
if (g_motorControl && g_motorControl->currentMode == MotorControl::PULSE_MODE) {
    bool dirPin = GPIO.out & (1 << DIR_PIN);
    if (g_motorControl->pulseDirection == 0) {
        // dir==0逻辑
    } else {
        // dir==1逻辑
    }
}
```

#### 新增方法实现
- `setpulsemode()`: 设置控制模式为脉冲模式
- `setPulseTarget()`: 设置目标位置、速度和方向
- `updatePulseControl()`: 执行脉冲控制逻辑
- `getCurrentPulseCount()`: 返回当前虚拟编码器计数

### 3. 全局变量添加 (`src/ble_manager.cpp`)

```cpp
uint8_t motor_mode = 0;        // 0=速度, 1=位置, 2=脉冲
uint32_t motor_pulse_target = 8388608;  // 脉冲模式目标位置
uint8_t motor_pulse_dir = 0;   // 脉冲模式方向
float motor_pulse_speed = 0;   // 脉冲模式速度
```

### 4. 主循环修改 (`src/main.cpp`)

#### 新的控制逻辑
```cpp
switch(motor_mode) {
    case 0: // 速度模式
        motor.setspeedmode();
        motor.setOutput(motor_speed);
        break;
    case 1: // 位置模式
        motor.setpositionmode();
        motor.setPosition(motor_steps);
        motor.updatePositionControl();
        break;
    case 2: // 脉冲模式
        motor.setpulsemode();
        motor.setPulseTarget(motor_pulse_target, motor_pulse_speed, motor_pulse_dir);
        motor.updatePulseControl();
        break;
}
```

#### 新增串口命令
```cpp
// 脉冲模式命令: pulse:目标位置 speed:速度 dir:方向
else if (value.find("pulse:") == 0) {
    // 解析命令参数
    // 设置脉冲模式参数
}

// 读取脉冲计数命令
else if (value == "read_pulse") {
    MySerial_1.printf("pulse_count:%u\n", motor.getCurrentPulseCount());
}
```

## 使用示例

### 串口命令示例

1. **向前移动608步 (dir=1)**
   ```
   pulse:8388000 speed:30.0 dir:1
   ```
   - 从8,388,608移动到8,388,000
   - 电机向motor_speed<0方向移动608步

2. **向前移动大量步数 (dir=0)**
   ```
   pulse:8390000 speed:50.0 dir:0
   ```
   - 从当前位置移动到8,390,000
   - 电机向motor_speed>0方向移动

3. **读取当前位置**
   ```
   read_pulse
   ```
   - 返回: `pulse_count:8388000`

### 编程接口示例

```cpp
// 设置脉冲模式
motor_mode = 2;
motor_pulse_target = 8388000;
motor_pulse_speed = 30.0;
motor_pulse_dir = 1;

// 或直接调用方法
motor.setpulsemode();
motor.setPulseTarget(8388000, 30.0, 1);
```

## 技术特点

### 1. 实时性
- 虚拟编码器在定时器中断中实时更新
- 确保每个电机步进都被准确计数

### 2. 精确性
- 24位计数器提供高精度位置控制
- 每步对应计数器变化1，无累积误差

### 3. 灵活性
- 支持两种方向映射模式
- 可设置任意目标位置和移动速度

### 4. 兼容性
- 与现有速度模式和位置模式完全兼容
- 保持原有API不变

## 测试验证

创建了完整的测试套件 (`examples/pulse_mode_test.cpp`)：
- 基本功能测试
- 串口命令测试
- 边界条件测试
- 精度测试

## 文档支持

- `PULSE_MODE_DOCUMENTATION.md`: 用户使用指南
- `PULSE_MODE_IMPLEMENTATION.md`: 技术实现详情
- `examples/pulse_mode_test.cpp`: 测试示例代码

## 总结

成功实现了虚拟编码器脉冲模式，为ESP32步进电机控制系统提供了第三种精确的位置控制方式。该实现具有高精度、实时性和良好的兼容性，完全满足用户需求。
