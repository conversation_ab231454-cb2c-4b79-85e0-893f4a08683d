# 虚拟编码器脉冲模式文档

## 概述

本文档描述了新增的第三种电机运动模式：虚拟编码器脉冲模式。这种模式使用一个虚拟的脉冲计数器来控制电机位置，提供了一种新的位置控制方式。

## 功能特性

### 虚拟编码器计数器
- **范围**: 0 ~ 16,777,215 (24位计数器)
- **初始值**: 8,388,608 (范围中点)
- **分辨率**: 每个电机步进脉冲对应计数器变化1

### 虚拟编码器计数
- **计数规则**: 无论在哪种模式下，虚拟编码器都会跟踪电机实际步数
- **DIR_PIN高电平**: 计数器递增（正向运动）
- **DIR_PIN低电平**: 计数器递减（反向运动）
- **溢出处理**:
  - 计数器>16777215时自动回绕到0
  - 计数器<0时自动回绕到16777215

### 方向控制（仅在脉冲模式下有效）
- **dir = 0**: 按正常方向运动到目标位置
- **dir = 1**: 按相反方向运动到目标位置

### 位置控制
系统根据当前计数器值与目标值的差异，自动计算需要移动的步数和方向。

## 使用方法

### 串口命令

#### 设置脉冲模式目标位置
```
pulse:目标位置 speed:速度 dir:方向
```

**参数说明:**
- `目标位置`: 0-16777215范围内的整数
- `速度`: 电机移动速度（浮点数）
- `方向`: 0或1

**示例:**
```
pulse:8388000 speed:30.0 dir:1
pulse:8390000 speed:50.0 dir:0
```

#### 读取当前脉冲计数
```
read_pulse
```

**响应格式:**
```
pulse_count:当前计数值
```

#### 重置脉冲计数器
```
reset_pulse:新值
reset_pulse
```

**参数说明:**
- `新值`: 0-16777215范围内的整数（可选）
- 不提供新值时重置为默认值8388608

**响应格式:**
```
pulse_reset:设置的值
```

**示例:**
```
reset_pulse:8388608
reset_pulse:0
reset_pulse
```

#### 调试脉冲模式
```
debug_pulse
```

**功能**: 显示脉冲模式的详细调试信息，包括当前计数、目标计数、方向、速度等。

#### 强制停止脉冲模式
```
stop_pulse
```

**功能**: 立即停止脉冲模式运动，切换到速度模式并停止电机。

#### 解锁脉冲模式
```
unlock_pulse
```

**功能**: 强制解锁脉冲模式，清除到达目标标志，允许重新设置目标位置。

#### 系统状态监控
```
system_status
```

**功能**: 显示系统运行状态，包括内存使用、CPU频率、运行时间等调试信息。

### 编程接口

#### 设置脉冲模式
```cpp
motor.setpulsemode();
```

#### 设置目标位置
```cpp
motor.setPulseTarget(target, speed, dir);
```

#### 更新脉冲控制
```cpp
motor.updatePulseControl();
```

#### 获取当前计数
```cpp
uint32_t count = motor.getCurrentPulseCount();
```

#### 重置计数器
```cpp
motor.resetPulseCount();        // 重置为默认值8388608
motor.resetPulseCount(1000000); // 重置为指定值
```

## 使用示例

### 示例1: 正常方向移动到目标位置
```
pulse:8388000 speed:30.0 dir:0
```
- 当前位置: 8,388,608
- 目标位置: 8,388,000
- 需要减少608步
- dir=0: 电机按正常方向运动（反向运动使计数器减少）

### 示例2: 相反方向移动到目标位置
```
pulse:8388000 speed:30.0 dir:1
```
- 当前位置: 8,388,608
- 目标位置: 8,388,000
- 需要减少608步
- dir=1: 电机按相反方向运动（正向运动但通过绕行方式到达目标）

## 技术实现

### 核心组件

1. **虚拟计数器更新**: 在定时器中断中实时更新虚拟编码器计数
2. **位置控制算法**: 基于目标位置与当前位置的差值计算移动方向和距离
3. **方向映射**: 根据dir参数将逻辑方向映射到物理电机方向

### 关键变量

- `virtualPulseCount`: 当前虚拟编码器计数值
- `targetPulseCount`: 目标脉冲计数值
- `pulseDirection`: 方向控制参数（0或1）
- `pulseSpeed`: 移动速度
- `pulseTargetReached`: 目标到达标志

### 控制流程

1. 用户设置目标位置、速度和方向
2. 系统计算当前位置与目标位置的差值
3. 根据差值和方向参数确定电机运动方向
4. 电机开始移动，每步更新虚拟计数器
5. 当计数器达到目标值时停止电机

## 注意事项

1. **范围限制**: 目标位置必须在0-16777215范围内
2. **方向理解**: dir参数影响计数器增减与电机方向的对应关系
3. **精度**: 每个电机步进对应计数器变化1，提供高精度位置控制
4. **停止条件**: 当虚拟计数器达到目标值时电机自动停止

## 兼容性

新的脉冲模式与现有的速度模式和位置模式完全兼容，可以通过`motor_mode`变量进行切换：
- `motor_mode = 0`: 速度模式
- `motor_mode = 1`: 位置模式  
- `motor_mode = 2`: 脉冲模式

