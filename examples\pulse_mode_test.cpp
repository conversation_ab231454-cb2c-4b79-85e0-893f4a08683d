/**
 * @file pulse_mode_test.cpp
 * @brief 虚拟编码器脉冲模式测试示例
 * @details 演示如何使用新的脉冲模式控制电机
 */

#include <Arduino.h>
#include "motor_control.h"

// 外部变量声明
extern MotorControl motor;
extern uint8_t motor_mode;
extern uint32_t motor_pulse_target;
extern uint8_t motor_pulse_dir;
extern float motor_pulse_speed;

/**
 * @brief 脉冲模式测试函数
 * @details 演示各种脉冲模式操作
 */
void testPulseMode() {
    Serial.println("=== 虚拟编码器脉冲模式测试 ===");
    
    // 读取初始位置
    uint32_t initialPos = motor.getCurrentPulseCount();
    Serial.printf("初始位置: %u\n", initialPos);
    
    // 测试1: 向前移动608步 (dir=1)
    Serial.println("\n测试1: 向前移动608步 (dir=1)");
    motor_pulse_target = 8388000;  // 目标位置
    motor_pulse_speed = 30.0;      // 速度
    motor_pulse_dir = 1;           // 方向
    motor_mode = 2;                // 设置为脉冲模式
    
    Serial.printf("目标位置: %u\n", motor_pulse_target);
    Serial.printf("移动方向: dir=%u (motor_speed<0方向)\n", motor_pulse_dir);
    Serial.printf("移动速度: %.1f\n", motor_pulse_speed);
    
    // 等待移动完成
    delay(5000);
    
    uint32_t currentPos = motor.getCurrentPulseCount();
    Serial.printf("当前位置: %u\n", currentPos);
    Serial.printf("实际移动步数: %d\n", (int32_t)currentPos - (int32_t)initialPos);
    
    // 测试2: 向前移动大量步数 (dir=0)
    Serial.println("\n测试2: 向前移动大量步数 (dir=0)");
    uint32_t newTarget = 8390000;
    motor_pulse_target = newTarget;
    motor_pulse_speed = 50.0;
    motor_pulse_dir = 0;           // 方向改为0
    
    Serial.printf("目标位置: %u\n", motor_pulse_target);
    Serial.printf("移动方向: dir=%u (motor_speed>0方向)\n", motor_pulse_dir);
    Serial.printf("移动速度: %.1f\n", motor_pulse_speed);
    
    uint32_t beforeMove = motor.getCurrentPulseCount();
    
    // 等待移动完成
    delay(8000);
    
    currentPos = motor.getCurrentPulseCount();
    Serial.printf("移动前位置: %u\n", beforeMove);
    Serial.printf("当前位置: %u\n", currentPos);
    Serial.printf("实际移动步数: %d\n", (int32_t)currentPos - (int32_t)beforeMove);
    
    // 测试3: 回到初始位置
    Serial.println("\n测试3: 回到初始位置");
    motor_pulse_target = initialPos;
    motor_pulse_speed = 40.0;
    motor_pulse_dir = 1;
    
    Serial.printf("目标位置: %u (初始位置)\n", motor_pulse_target);
    
    beforeMove = motor.getCurrentPulseCount();
    
    // 等待移动完成
    delay(10000);
    
    currentPos = motor.getCurrentPulseCount();
    Serial.printf("移动前位置: %u\n", beforeMove);
    Serial.printf("最终位置: %u\n", currentPos);
    Serial.printf("与初始位置差值: %d\n", (int32_t)currentPos - (int32_t)initialPos);
    
    // 停止电机
    motor_mode = 0;  // 切换回速度模式
    motor_pulse_speed = 0;
    
    Serial.println("\n=== 测试完成 ===");
}

/**
 * @brief 串口命令测试函数
 * @details 演示通过串口命令控制脉冲模式
 */
void testSerialCommands() {
    Serial.println("=== 串口命令测试 ===");
    Serial.println("可以发送以下命令进行测试:");
    Serial.println("1. pulse:8388000 speed:30.0 dir:1");
    Serial.println("2. pulse:8390000 speed:50.0 dir:0");
    Serial.println("3. read_pulse");
    Serial.println("4. pulse:8388608 speed:40.0 dir:1  (回到中心位置)");
    Serial.println("");
    
    // 显示当前状态
    Serial.printf("当前脉冲计数: %u\n", motor.getCurrentPulseCount());
    Serial.printf("当前电机模式: %u\n", motor_mode);
    
    if (motor_mode == 2) {
        Serial.printf("脉冲模式参数:\n");
        Serial.printf("  目标位置: %u\n", motor_pulse_target);
        Serial.printf("  速度: %.1f\n", motor_pulse_speed);
        Serial.printf("  方向: %u\n", motor_pulse_dir);
    }
}

/**
 * @brief 边界测试函数
 * @details 测试脉冲计数器的边界条件
 */
void testBoundaryConditions() {
    Serial.println("=== 边界条件测试 ===");
    
    uint32_t currentPos = motor.getCurrentPulseCount();
    Serial.printf("当前位置: %u\n", currentPos);
    
    // 测试接近最大值
    Serial.println("测试接近最大值 (16777215)");
    motor_pulse_target = 16777200;
    motor_pulse_speed = 100.0;
    motor_pulse_dir = 0;
    motor_mode = 2;
    
    delay(5000);  // 等待移动
    
    currentPos = motor.getCurrentPulseCount();
    Serial.printf("移动后位置: %u\n", currentPos);
    
    // 测试接近最小值
    Serial.println("测试接近最小值 (0)");
    motor_pulse_target = 15;
    motor_pulse_speed = 100.0;
    motor_pulse_dir = 1;
    
    delay(5000);  // 等待移动
    
    currentPos = motor.getCurrentPulseCount();
    Serial.printf("移动后位置: %u\n", currentPos);
    
    // 回到中心位置
    Serial.println("回到中心位置");
    motor_pulse_target = 8388608;
    motor_pulse_speed = 80.0;
    motor_pulse_dir = 0;
    
    delay(8000);  // 等待移动
    
    currentPos = motor.getCurrentPulseCount();
    Serial.printf("最终位置: %u\n", currentPos);
    
    motor_mode = 0;  // 停止
    Serial.println("=== 边界测试完成 ===");
}

/**
 * @brief 精度测试函数
 * @details 测试小步数移动的精度
 */
void testPrecision() {
    Serial.println("=== 精度测试 ===");
    
    uint32_t startPos = motor.getCurrentPulseCount();
    Serial.printf("起始位置: %u\n", startPos);
    
    // 进行多次小步数移动
    for (int i = 1; i <= 5; i++) {
        Serial.printf("\n第%d次移动: +%d步\n", i, i);
        
        motor_pulse_target = startPos + i;
        motor_pulse_speed = 20.0;
        motor_pulse_dir = 0;
        motor_mode = 2;
        
        delay(2000);  // 等待移动完成
        
        uint32_t currentPos = motor.getCurrentPulseCount();
        int32_t actualMove = (int32_t)currentPos - (int32_t)startPos;
        
        Serial.printf("  目标步数: %d\n", i);
        Serial.printf("  实际步数: %d\n", actualMove);
        Serial.printf("  误差: %d\n", actualMove - i);
        
        startPos = currentPos;
    }
    
    motor_mode = 0;  // 停止
    Serial.println("\n=== 精度测试完成 ===");
}
