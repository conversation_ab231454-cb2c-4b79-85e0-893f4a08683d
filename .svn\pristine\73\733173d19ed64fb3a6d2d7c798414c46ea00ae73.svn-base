#include "serial_command.h"
#include "preferences_manager.h"
#include "ble_manager.h"  // 包含DeviceIdentifier
#include <Arduino.h>
#include "motor_control.h"
#include <USB.h>

// Skywatcher protocol constants
#define SKYWATCHER_LEADING_CHAR ':'
#define SKYWATCHER_TRAILING_CHAR 0x0D
#define STEPS_PER_REVOLUTION_AXIS1 32767  // Adjust based on your encoder
#define STEPS_PER_REVOLUTION_AXIS2 32767  // Adjust based on your encoder

// Debug flag - set to true to enable debugging output
#define DEBUG_SKYWATCHER false
#define HEXI(c) (((c) < 'A') ? ((c) - '0') : ((c) - 'A') + 10)

// Pre-computed responses for faster processing
// RESPONSE_VERSION 现在动态生成以确保唯一性
static const char* RESPONSE_GRID_AXIS1 = "=4E2000\r";
static const char* RESPONSE_GRID_AXIS2 = "=4E2000\r";
static const char* RESPONSE_TIMER_FREQ = "=0024F4\r";
static const char* RESPONSE_HIGH_SPEED = "=01\r";
static const char* RESPONSE_STATUS = "=101\r";
static const char* RESPONSE_STATUS1 = "=111\r";
static const char* RESPONSE_STATUS2 = "=100\r";
static const char* RESPONSE_STATUS3 = "=110\r";

static const char* RESPONSE_STATUS_0 = "=201\r";
static const char* RESPONSE_STATUS_1 = "=211\r";
static const char* RESPONSE_STATUS_2 = "=200\r";
static const char* RESPONSE_STATUS_3 = "=210\r";

// static const char* RESPONSE_STATUS2 = "=201\r";
// static const char* RESPONSE_STATUS3 = "=211\r";
static const char* RESPONSE_FEATURE = "=009000\r";
static const char* RESPONSE_OK = "=\r";

PreferencesManager prefsManager;
extern uint16_t tle_angle;
extern float curr_speed;
extern float motor_speed;
extern bool motormode;
extern HardwareSerial MySerial_0;
extern uint16_t remote_tle_angle;
extern bool remote_encoder_received;
extern bool remote_speed_received;
extern float remote_motor_speed;
bool dir_ra = 0;
bool dir_dec = 0;
bool ra_speed_status=0;
bool dec_speed_status=0;
uint32_t steps_360 = 5120000;
float ra_step = 0;
int ra_res = 0;
int ra_steps1 = 0;


void SerialCommand::setup() {
    // Serial.begin(115200);
    // Serial.println("Commands: p[val] i[val] d[val] c (calibrate)");
    //USB.begin();
    Serial.begin(9600);
    //while (!Serial); // 等待 USB 连接

  // 禁用硬件 UART0 的 TXD0/RXD0 引脚（GPIO43/44）
    pinMode(43, INPUT_PULLDOWN);
    pinMode(44, INPUT_PULLDOWN);
    // Serial.println("Commands: p[val] i[val] d[val] c (calibrate)"); // Commented out to avoid interference with Skywatcher protocol
}
// void SerialCommand::process(PIDController& pid, Encoder& encoder, MotorControl& motor) {
//     if(Serial.available()) {
//         char cmd = Serial.read();
//         switch(cmd) {
//             case 'p':
//             case 'i':
//             case 'd': {
//                 float value = Serial.parseFloat();
//                 handlePIDCommand(cmd, value, pid);
//                 break;
//             }
//             case 'c':
//                 handleCalibrateCommand(encoder, motor);
//                 break;
//             case 's':
//                 handleStatusCommand(pid, encoder, motor);
//                 break;
//             case 't':
//                 position_control_motorCommand(pid, encoder, motor);;
//                 break;    
//         }
//     }
// }

// void SerialCommand::handlePIDCommand(char cmd, float value, PIDController& pid) {
//     prefsManager.begin();
//     switch(cmd) {
//         case 'p':
//             prefsManager.putFloat("kp", value);
//             pid.setKp(value);
//             break;
//         case 'i':
//             prefsManager.putFloat("ki", value);
//             pid.setKi(value);
//             break;
//         case 'd':
//             prefsManager.putFloat("kd", value);
//             pid.setKd(value);
//             break;
//     }
//     prefsManager.end();
//     Serial.print("Set "); Serial.print(cmd); 
//     Serial.print(" to: "); Serial.println(value);
// }

// void SerialCommand::handleCalibrateCommand(Encoder& encoder, MotorControl& motor) {
//     encoder.calibrate(motor);
// }


// void SerialCommand::handleStatusCommand(PIDController& pid, Encoder& encoder, MotorControl& motor) {
//     float position = tle5012_to_angle(tle_angle);
//     Serial.println("---- Motor Status ----");
//     Serial.print("Position (rad): "); 
//     Serial.println(position, 6);
//     Serial.println("----------------------");
// }

// void SerialCommand::position_control_motorCommand(PIDController& pid, Encoder& encoder, MotorControl& motor)
// {

//     motor.setpositionmode();
//     motor.setPosition(100);
//     motor.updatePositionControl();
//     Serial.println("speeed");
//     Serial.print(curr_speed);

// }
void SerialCommand::process(Encoder& encoder, MotorControl& motor) {
    static char commandBuffer[64];
    static int bufferIndex = 0;
    static unsigned long lastCharTime = 0;

    // Batch read all available characters for better performance
    while(Serial.available() > 0) {
        char c = Serial.read();
        lastCharTime = millis();

        // Debug: Log all received characters
        if(DEBUG_SKYWATCHER) {
            Serial.printf("[DEBUG] Received char: 0x%02X (%c)\n", (unsigned char)c, (c >= 32 && c <= 126) ? c : '?');
        }

        if(c == SKYWATCHER_TRAILING_CHAR) { // Carriage return - end of command
            commandBuffer[bufferIndex] = '\0'; // Null terminate
            if(bufferIndex >= 3 && commandBuffer[0] == SKYWATCHER_LEADING_CHAR) {
                processSkywatcherCommandFast(commandBuffer,encoder, motor);
            }
            bufferIndex = 0; // Reset buffer
        } else if(c >= 0x20 && c <= 0x7E) { // Only accept printable ASCII characters
            if(bufferIndex < 63) { // Prevent buffer overflow
                commandBuffer[bufferIndex++] = c;
            } else {
                bufferIndex = 0; // Reset on overflow
            }
        }
        // Skip all other characters (control chars, non-ASCII, etc.)
    }

    // Clear buffer if no data received for too long (timeout)
    if(bufferIndex > 0 && (millis() - lastCharTime) > 1000) {
        bufferIndex = 0;
    }
}

// Helper function to convert long to hex string (uppercase)
String SerialCommand::longToHexString(long value) {
    String hex = String(value, HEX);
    hex.toUpperCase();
    // Pad with zeros to make it 6 characters
    while(hex.length() < 6) {
        hex = "0" + hex;
    }
    return hex;
}

// Helper function to convert hex string to long
long SerialCommand::hexStringToLong(String hexStr) {
    return strtol(hexStr.c_str(), NULL, 16);
}

// Fast command processing using C-style strings for better performance
void SerialCommand::processSkywatcherCommandFast(char* command, Encoder& encoder, MotorControl& motor) {
    int len = strlen(command);

    // Remove trailing dot if present
    if(len > 0 && command[len-1] == '.') {
        command[len-1] = '\0';
        len--;
    }

    if(len < 3) return; // Invalid command

    char cmd = command[1];  // Command character
    char axis = command[2]; // Axis character ('1' or '2')
    

    // Debug: Log the command
    if(DEBUG_SKYWATCHER) {
        Serial.printf("[DEBUG] Processing command: %s\n", command);
    }

    // Fast command processing with pre-computed responses
    switch(cmd) {
        case 'e': // InquireMotorBoardVersion
            // 使用基于MAC地址的唯一版本响应
            Serial.print(DeviceIdentifier::getUniqueVersionResponse());
            break;

        case 'a': // InquireGridPerRevolution
            if(axis == '1') {
                // char hexStr[7];
                // long2Revu24str(steps_360, hexStr);
                // Serial.printf("=%s\r", hexStr);
                Serial.print(RESPONSE_GRID_AXIS1);
                
            } else if(axis == '2'){
                // char hexStr[7];
                // long2Revu24str(steps_360, hexStr);
                // Serial.printf("=%s\r", hexStr);
                Serial.print(RESPONSE_GRID_AXIS1);
            }
            break;

        case 'b': // InquireTimerInterruptFreq
            Serial.print(RESPONSE_TIMER_FREQ);
            break;

        case 'g': // InquireHighSpeedRatio
            Serial.print(RESPONSE_HIGH_SPEED);
            break;

        case 'q': // GetFeatureCmd
            if(len >= 9 && strncmp(&command[3], "010000", 6) == 0) {
                Serial.print(RESPONSE_FEATURE);
            } else {
                Serial.print("=000000\r");
            }
            break;

        case 'f': // GetAxisStatus
            if(axis == '2') {
                
                    if(curr_speed)
                    {
                        Serial.print(RESPONSE_STATUS1);
                    }
                    else
                    {
                        Serial.print(RESPONSE_STATUS);
                    }
               
            } else if(axis == '1'){
                 // For axis 2, request encoder value from remote device
                MySerial_0.println("motorcor_speed");
                MySerial_0.flush();

                // Wait for response with timeout
                unsigned long startTime = millis();
                remote_speed_received = false;

                while (!remote_speed_received && (millis() - startTime) < 100) {
                    // Small delay to allow response processing
                    delay(10);
                }

                if (remote_speed_received) {
                    // Use received remote encoder value
                    if(remote_motor_speed)
                    {
                        Serial.print(RESPONSE_STATUS1);

                    }
                    else
                    {
                        Serial.print(RESPONSE_STATUS);
                    }
                   
                    //Serial.printf("=%s\r", hexStr);
                } else {

                    Serial.print("=0\r");
                }
                /////////////////////////////////////////

            }
            //Serial.print(RESPONSE_STATUS);
            break;
        case 'P': // SetST4GuideRateCmd
            Serial.print(RESPONSE_OK);
            break;
        case 'O': // SetSnapPort
            Serial.print(RESPONSE_OK);
            break;

        case 'j': // GetAxisPosition
            if(axis == '2') {
                // Get current position from encoder for axis 1
                float encoderPos = encoder.getPosition();
                // Convert radians to encoder steps
                uint32_t steps = tle_angle;
                char hexStr[7];
                long2Revu24str(steps, hexStr);
                // Convert to 24-bit position value and format as hex
                //steps = steps & 0xFFFFFF; // Mask to 24 bits
                Serial.printf("=%s\r", hexStr);
            } else if(axis == '1'){
                // For axis 2, request encoder value from remote device
                MySerial_0.println("read_encoder");
                MySerial_0.flush();

                // Wait for response with timeout
                unsigned long startTime = millis();
                remote_encoder_received = false;

                while (!remote_encoder_received && (millis() - startTime) < 100) {
                    // Small delay to allow response processing
                    delay(10);
                }

                if (remote_encoder_received) {
                    // Use received remote encoder value
                    uint32_t steps = remote_tle_angle;
                    char hexStr[7];
                    long2Revu24str(steps, hexStr);
                    // Convert to 24-bit position value and format as hex
                    //steps = steps & 0xFFFFFF; // Mask to 24 bits
                    Serial.printf("=%s\r", hexStr);
                } else {
                    // Fallback to default value if no response
                    Serial.print("=0\r");
                }
            }
            break;

        case 'F': // Initialize
           // motormode = false;
            motor_speed = 0;
            //MySerial_0.println("stop_ud\n");
            MySerial_0.flush();
            Serial.print(RESPONSE_OK);
            break;

        case 'L': // InstantAxisStop
            if(axis == '2') {
                //motormode = false;
                motor_speed = 0;
                Serial.print(RESPONSE_OK);
            } else if(axis == '1'){
                MySerial_0.println("stop_ud\n");
                MySerial_0.flush();
                Serial.print(RESPONSE_OK);
            }
            break;

        case 'K': // NotInstantAxisStop
            if(axis == '2') {
                //motormode = false;
                motor_speed = 0;
                Serial.print(RESPONSE_OK);
                dec_speed_status = 0;
            } else if(axis == '1'){
                MySerial_0.println("stop_ud\n");
                MySerial_0.flush();
                Serial.print(RESPONSE_OK);
                ra_speed_status = 0;
            }
            break;
        case 'E': // SetAxisPositionCmd
            if(len > 3) {
                // Parse hex position (simple hex string to long conversion)
                long newPosition = strtol(&command[3], NULL, 16);
                // Convert steps back to radians and set encoder
                // This would require an encoder.setPosition() method
            }
            Serial.print(RESPONSE_OK);
            break;

        case 'G': // SetMotionMode
            if(axis == '2') {
                if(len >= 4) {
                // dir_dec = command[4];
                if(command[4]== '1')
                {
                    dir_ra = 1;
                }
                if(command[4]== '0'){
                    dir_ra = 0;
                }
                if((command[3]== '1')||(command[3]== '3'))
                {
                    //dir_dec = 1;
                    motormode = false;
                }
                if((command[3]== '0')||(command[3]== '2'))
                {
                    //dir_dec = 1;
                    motormode = true;
                }
            }
            } 
            if (axis == '1'){
                if(len >= 4) {
                // dir_ra = command[4];
                if(command[4]== '1')
                {
                    dir_dec = 1;
                }
                else{
                    dir_dec = 0;
                }
            }
            }
            // if(len >= 4) {
            //     char mode = command[3];
            //     // Mode: 0=goto, 1=slow goto, 2=low speed slew, 3=high speed slew
            //     // You can implement different motor control modes here
            // }
            Serial.print(RESPONSE_OK);
            break;

        case 'H': // SetGotoTargetIncrement
            if(len > 3) {
                long increment = strtol(&command[3], NULL, 16);
                // Store goto target increment
            }
            Serial.print(RESPONSE_OK);
            break;

        case 'M': // SetBreakPointIncrement
            if(len > 3) {
                long increment = strtol(&command[3], NULL, 16);
                // Store break point increment
            }
            Serial.print(RESPONSE_OK);
            break;

        case 'S': // SetGotoTarget
             if(axis == '2') {
                
                uint32_t res = 0;
                if (len >= 4) {
                    res = HEXI(command[7]);
                    res <<= 4;
                    res |= HEXI(command[8]);
                    res <<= 4;
                    res |= HEXI(command[5]);
                    res <<= 4;
                    res |= HEXI(command[6]);
                    res <<= 4;
                    res |= HEXI(command[3]);
                    res <<= 4;
                    res |= HEXI(command[4]);

                    motor_steps = (float)res / (float)STEPS_PER_REVOLUTION_AXIS1 * 360;
                    // motormode = true;
                    
                }
                Serial.print(RESPONSE_OK);
            } 
            if (axis == '1'){

                uint32_t res = 0;
                if (len >= 4) {
                    res = HEXI(command[7]);
                    res <<= 4;
                    res |= HEXI(command[8]);
                    res <<= 4;
                    res |= HEXI(command[5]);
                    res <<= 4;
                    res |= HEXI(command[6]);
                    res <<= 4;
                    res |= HEXI(command[3]);
                    res <<= 4;
                    res |= HEXI(command[4]);
                    //float ra_step;
                    ra_res = res;
                    ra_step = (float)res / (float)STEPS_PER_REVOLUTION_AXIS1 * 360;
                    int ra_steps = ra_step;
                    // 使用基于步数的合理速度值，与其他地方的计算方式保持一致
                    // 参考其他地方的速度计算：(float)res / 100000
                    float calculated_speed = 30;
                    MySerial_0.printf("down:%.8f speed:%.8f\n",ra_step,calculated_speed);
                    MySerial_0.flush(); // 强制刷新发送缓冲区
                }
                Serial.print(RESPONSE_OK);
            }
            break;
            // if(len > 3) {
            //     long target = strtol(&command[3], NULL, 16);
            //     //motor_steps = target;
            //     motormode = true;
            //     int dec_step;
            //     // Convert steps to radians and set target position
            //     // float targetRad;
            //     if(axis == '1') {
            //         motor_steps = target;
            //     } else {
            //         dec_step = target;
            //     }
            //     // if(axis == '1') {
            //     //     motor_steps = target / STEPS_PER_REVOLUTION_AXIS1 * 360;
            //     // } else {
            //     //     dec_step = target / STEPS_PER_REVOLUTION_AXIS2 * 360;
            //     // }
            //     //motor.setPosition(targetRad);
            //     //motor.setpositionmode(); // Switch to position control mode
            // }
            // Serial.print(RESPONSE_OK);
            // break;

        case 'U': // SetBreakStep
            Serial.print(RESPONSE_OK);
            break;
        case 'I':
            if(axis == '2') {
                
                uint32_t res = 0;
                if (len >= 4) {
                    res = HEXI(command[7]);
                    res <<= 4;
                    res |= HEXI(command[8]);
                    res <<= 4;
                    res |= HEXI(command[5]);
                    res <<= 4;
                    res |= HEXI(command[6]);
                    res <<= 4;
                    res |= HEXI(command[3]);
                    res <<= 4;
                    res |= HEXI(command[4]);
                    if(dir_ra)
                    {
                        motor_speed = (float)res / 100000;
                    }
                    else
                    {
                        motor_speed = (float)res / 100000;
                        motor_speed = -motor_speed;
                    }
                    if(motor_speed)
                    {
                        dec_speed_status = 1;
                    }
                }
                Serial.print(RESPONSE_OK);
            } 
            if (axis == '1'){
                
                uint32_t res = 0;
                if (len >= 4) {
                    res = HEXI(command[7]);
                    res <<= 4;
                    res |= HEXI(command[8]);
                    res <<= 4;
                    res |= HEXI(command[5]);
                    res <<= 4;
                    res |= HEXI(command[6]);
                    res <<= 4;
                    res |= HEXI(command[3]);
                    res <<= 4;
                    res |= HEXI(command[4]);
                    if(dir_dec)
                    {
                        //motor_speed = res/10;
                        float speed = (float)res / 100000;
                        MySerial_0.printf("Tup:%.8f\n", speed);
                        MySerial_0.flush(); // 强制刷新发送缓冲区
                        if(speed)
                        {
                           ra_speed_status = 1;
                        }
                    }
                    else
                    {
                        float speed = (float)res / 100000;
                        //speed = -speed;
                        MySerial_0.printf("Tdown:%.8f\n", speed);
                        MySerial_0.flush(); // 强制刷新发送缓冲区
                        if(speed)
                        {
                           ra_speed_status = 1;
                        }
                    }
                }
                Serial.print(RESPONSE_OK);

            }
            break;
         // SetStepPeriod
        case 'D': // GetStepPeriod
            Serial.print("=000000\r");
            break;

        case 'J': // StartMotion
            //motor.updatePositionControl();
            Serial.print(RESPONSE_OK);
            break;

        case 'B': // ActivateMotor
        case 'd': // GetHomePosition or InquireAuxEncoder
            Serial.print("=1000\r");
            break;

        case 'W': // SetFeatureCmd
        case 'V': // SetPolarScopeLED
        default:
            Serial.print(RESPONSE_OK);
            break;
    }
}

// Legacy function for compatibility - now calls the fast version
void SerialCommand::processSkywatcherCommand(String command, PIDController& pid, Encoder& encoder, MotorControl& motor) {
    char commandBuffer[64];
    strncpy(commandBuffer, command.c_str(), sizeof(commandBuffer) - 1);
    commandBuffer[sizeof(commandBuffer) - 1] = '\0';
    processSkywatcherCommandFast(commandBuffer,encoder, motor);
}

void SerialCommand::long2Revu24str(uint32_t n, char *str)
{
    char hexa[16] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
    str[0]        = hexa[(n & 0xF0) >> 4];
    str[1]        = hexa[(n & 0x0F)];
    str[2]        = hexa[(n & 0xF000) >> 12];
    str[3]        = hexa[(n & 0x0F00) >> 8];
    str[4]        = hexa[(n & 0xF00000) >> 20];
    str[5]        = hexa[(n & 0x0F0000) >> 16];
    str[6]        = '\0';
}

// void SerialCommand::process(PIDController& pid, Encoder& encoder, MotorControl& motor) {
//     if(Serial.available() > 0) {
//         // Read until newline or buffer full
//         String input = Serial.readStringUntil('\n');
//         input.trim(); // Remove any whitespace/newlines
//         std::string value(input.c_str());
        
//         GPIO.out_w1tc = (1 << 10); // 置高
//         // 解析命令
//         if (value.find("Turn_up:") == 0) {
//             float speed = atof(value.substr(8).c_str());
//             motor.setOutput(speed);
//         } 
//         else if (value.find("Turn_down:") == 0) {
//             float speed = atof(value.substr(10).c_str());
//             motor.setOutput(-speed);
//         }
//         else if (value.find("Turn_left:") == 0) {
//             float speed = atof(value.substr(10).c_str());
//             motor.setOutput(speed);
//         }
//         else if (value.find("Turn_right:") == 0) {
//             float speed = atof(value.substr(11).c_str());
//             motor.setOutput(-speed);
//         }
//         else if (value == "stop_updown") {
//             motor.setOutput(0);
//         }
//         else if (value == "stop_rightleft") {
//             motor.setOutput(0);
//         }
//         else if (value.find("up:") == 0 || value.find("down:") == 0 || 
//                  value.find("left:") == 0 || value.find("right:") == 0) {
//             // 解析步数和速度
//             size_t colon_pos = value.find(":");
//             size_t space_pos = value.find(" ");
//             if (space_pos != std::string::npos) {
//                 int steps = atoi(value.substr(colon_pos+1, space_pos-colon_pos-1).c_str());
//                 float speed = atof(value.substr(space_pos+7).c_str()); // "speed:" is 6 chars
//                 Serial.printf("Steps: %d, Speed: %f\n", steps, speed);
//                 // TODO: 实现步数控制逻辑
//                 motor.setOutput(speed);
//             }
//         }
//     }
// }
