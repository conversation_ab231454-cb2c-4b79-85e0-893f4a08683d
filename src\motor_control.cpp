#include "motor_control.h"
#include <Arduino.h>
#include <driver/timer.h>
#include "encoder.h"

// TMC2226控制引脚定义
#define STEP_PIN 13
#define DIR_PIN 9
#define STEP_DELAY_US 100  // 步进脉冲宽度(微秒)

// 硬件定时器配置
hw_timer_t *timer = NULL;
portMUX_TYPE timerMux = portMUX_INITIALIZER_UNLOCKED;
volatile uint32_t stepInterval = 0;
volatile uint32_t stepCount = 0;
extern uint16_t tle_angle;
float curr_speed = 0;

// 全局指针用于在中断中访问MotorControl实例
static MotorControl* g_motorControl = nullptr;

void IRAM_ATTR onTimer() {
  // 更高效的GPIO操作 - 直接设置/清除寄存器
  if (GPIO.out & (1 << STEP_PIN)) {
    GPIO.out_w1tc = (1 << STEP_PIN); // 清除STEP_PIN
  } else {
    GPIO.out_w1ts = (1 << STEP_PIN); // 设置STEP_PIN
    portENTER_CRITICAL_ISR(&timerMux);
    stepCount++;

    // 虚拟编码器在所有模式下都更新，始终跟踪电机实际步数
    if (g_motorControl) {
      // 根据DIR_PIN的实际状态更新虚拟编码器
      // DIR_PIN高电平 = 正向运动，计数器递增
      // DIR_PIN低电平 = 反向运动，计数器递减
      bool dirPin = GPIO.out & (1 << DIR_PIN);

      if (dirPin) {
        // 正向运动，计数器递增
        if (g_motorControl->virtualPulseCount < 16777215) {
          g_motorControl->virtualPulseCount++;
        }
      } else {
        // 反向运动，计数器递减
        if (g_motorControl->virtualPulseCount > 0) {
          g_motorControl->virtualPulseCount--;
        }
      }
    }

    portEXIT_CRITICAL_ISR(&timerMux);
  }
}

void MotorControl::init() {
  // 设置全局指针以便在中断中访问
  g_motorControl = this;

  // 配置GPIO为输出模式并设置初始状态
  GPIO.enable_w1ts = (1 << STEP_PIN) | (1 << DIR_PIN) | (1 << 10) | (1 << 11);
  GPIO.out_w1tc = (1 << STEP_PIN);  // STEP_PIN初始低电平
  GPIO.out_w1ts = (1 << DIR_PIN);
  GPIO.out_w1tc = (1 << 10);       // GPIO10初始低电平
  GPIO.out_w1tc = (1 << 11);       // GPIO11初始低电平
  //GPIO.out_w1tc = (1 << 38);       // GPIO38初始低电平
  pinMode(38, OUTPUT);
  digitalWrite(38, 0);



  // 初始化硬件定时器 (定时器0, 80MHz/16=5MHz, 0.2us分辨率)
  timer = timerBegin(0, 8, true);
  timerAttachInterrupt(timer, &onTimer, true);

  // 初始化编码器和PID控制器
  encoder = new Encoder();
  encoder->init();
  // pidController = PIDController(1.0, 0.1, 0.05); // 默认PID参数
}

/**
 * @brief 设置电机输出(速度+方向)
 * @param output 输出值(-1.0~1.0)
 *   - 正数: 正转
 *   - 负数: 反转
 *   - 绝对值大小表示速度
 */
void MotorControl::setOutput(float output) {
  static int counter = 0;
  unsigned long currentTime = micros();
  float deltaTime = (currentTime - lastUpdateTime) / 1000000.0f;
  lastUpdateTime = currentTime;
  curr_speed = output; 
  // 设置电机方向 - 直接寄存器操作
  if(output > 0) {
    GPIO.out_w1ts = (1 << DIR_PIN); // 置高
  } else if(output < 0) {
    GPIO.out_w1tc = (1 << DIR_PIN); // 置低
  }
  else{
    // output == 0，不改变 DIR_PIN 状态
  }
  
  // 临界区保护 - 修改步进参数
  portENTER_CRITICAL(&timerMux);
  // 计算目标速度
  targetSpeed = abs(output) * 200; // 速度=output*200 steps/s
  targetSpeed = (targetSpeed > 8000) ? 8000 : targetSpeed; // 最大500 steps/s
  
  // 应用加速度限制
  if(targetSpeed > currentSpeed) {
    currentSpeed += maxAcceleration * deltaTime;
    if(currentSpeed > targetSpeed) currentSpeed = targetSpeed;
  } else if(targetSpeed < currentSpeed) {
    currentSpeed -= maxAcceleration * deltaTime;
    if(currentSpeed < targetSpeed) currentSpeed = targetSpeed;
  }
  portEXIT_CRITICAL(&timerMux);
  
  // 设置步进频率
  if (targetSpeed == 0 && abs(currentSpeed) < 1500) {
  timerAlarmDisable(timer);   // 关闭定时器
  currentSpeed = 0;           // 强制速度归零
  GPIO.out_w1tc = (1 << STEP_PIN); // 确保STEP为低电平
} else if(currentSpeed >= 10) {
  timerAlarmWrite(timer, 1000000 / currentSpeed, true);
  timerAlarmEnable(timer);
}


    uint16_t local_angle = tle_angle;
    float currentAngle = tle5012_to_angle(local_angle);
    
    // 过零检测和圈数更新
    float angleDiff = currentAngle - lastAngle;
    if (angleDiff > 300.0f) {  // 逆时针过零 (360->0)
      totalRevolutions--;
    } else if (angleDiff < -300.0f) {  // 顺时针过零 (0->360)
      totalRevolutions++;
    }
    lastAngle = currentAngle;
  // printf("speed Value: %f",currentSpeed);
}

/**
 * @brief 电机输出接口(兼容原有代码)
 * @param theta 角度(未使用)
 * @param effort 输出力度(0-255)
 *   - 转换为-1.0~1.0后调用setOutput
 */
// void MotorControl::output(float theta, uint8_t effort) {
//   // 将0-255的effort转换为-1.0~1.0的输出
//   setOutput(effort/255.0f); 
// }

// 位置控制相关实现
void MotorControl::setPosition(float target) {
  targetPosition = target;
}

void MotorControl::setControlMode(ControlMode mode) {
  currentMode = mode;
  if (mode == POSITION_MODE) {
    pidController.reset();
  } else if (mode == PULSE_MODE) {
    // 重置脉冲模式状态
    pulseTargetReached = false;
  }
}

void MotorControl::updatePositionControl() {
  if (currentMode == POSITION_MODE) {
    portENTER_CRITICAL(&timerMux);
    uint16_t local_angle = tle_angle;
    portEXIT_CRITICAL(&timerMux);
    float currentAngle = tle5012_to_angle(local_angle);
    
    // 过零检测和圈数更新
    float angleDiff = currentAngle - lastAngle;
    if (angleDiff > 300.0f) {  // 逆时针过零 (360->0)
      totalRevolutions--;
    } else if (angleDiff < -300.0f) {  // 顺时针过零 (0->360)
      totalRevolutions++;
    }
    lastAngle = currentAngle;
    
    // 计算绝对位置 (考虑多圈)
    float absolutePos = totalRevolutions * 360.0f + currentAngle;
    
    // PID控制
    float error = targetPosition - absolutePos;
    float output = pidController.calculate(error);
    
    if(fabs(error) < 0.05f && fabs(output) < 0.05f) {
      output = 0;
      pidController.reset();
    }
    
    // curr_speed = output; 
    setOutput(output);
  }
}

float MotorControl::getCurrentPosition() const {
  // return encoder ? encoder->getPosition() : 0;
  return encoder ? tle5012_to_angle(tle5012b_read_angle(TLE5012B_SPI,(gpio_num_t)12)) : 0;
}

float MotorControl::getTargetPosition() const {
  return targetPosition;
}

void MotorControl::setpositionmode()
{

  setControlMode(POSITION_MODE);
}
  
void MotorControl::setspeedmode()
{
  setControlMode(SPEED_MODE);
}

void MotorControl::setpulsemode()
{
  setControlMode(PULSE_MODE);
}

void MotorControl::setPulseTarget(uint32_t target, float speed, uint8_t dir) {
  // 确保目标值在有效范围内
  if (target <= 16777215) {
    targetPulseCount = target;
    pulseSpeed = abs(speed); // 确保速度为正值
    pulseDirection = dir & 1; // 确保方向只能是0或1
    pulseTargetReached = false;

    // 如果目标位置与当前位置相同，直接标记为已到达
    int32_t stepDiff = (int32_t)target - (int32_t)virtualPulseCount;
    if (abs(stepDiff) <= 3) {
      pulseTargetReached = true;
      setOutput(0); // 停止电机
    }
  }
}

void MotorControl::updatePulseControl() {
  if (currentMode == PULSE_MODE && !pulseTargetReached) {
    // 计算需要移动的步数
    int32_t stepDiff = (int32_t)targetPulseCount - (int32_t)virtualPulseCount;

    // 添加死区，避免在目标位置附近摆动
    if (abs(stepDiff) <= 3) {
      // 已到达目标位置（允许±3步的误差），停止电机
      setOutput(0);
      pulseTargetReached = true;
      return;
    }

    // 根据步数差值确定需要的运动方向
    // stepDiff > 0: 需要增加计数器（电机正向运动，DIR_PIN=高）
    // stepDiff < 0: 需要减少计数器（电机反向运动，DIR_PIN=低）
    bool needForward = (stepDiff > 0);

    // 根据dir参数决定实际的电机运动方向
    float outputSpeed = 0;

    if (pulseDirection == 0) {
      // dir==0: 按正常方向运动
      // 需要正向时电机正向运动，需要反向时电机反向运动
      outputSpeed = needForward ? pulseSpeed : -pulseSpeed;
    } else {
      // dir==1: 按相反方向运动
      // 需要正向时电机反向运动，需要反向时电机正向运动
      outputSpeed = needForward ? -pulseSpeed : pulseSpeed;
    }

    // 根据剩余步数调整速度，接近目标时减速
    if (abs(stepDiff) < 50) {
      outputSpeed *= 0.3f; // 接近目标时大幅减速
    } else if (abs(stepDiff) < 200) {
      outputSpeed *= 0.6f; // 较接近目标时适度减速
    }

    setOutput(outputSpeed);
  }
}

uint32_t MotorControl::getCurrentPulseCount() const {
  return virtualPulseCount;
}

void MotorControl::resetPulseCount(uint32_t value) {
  // 确保值在有效范围内
  if (value <= 16777215) {
    portENTER_CRITICAL(&timerMux);
    virtualPulseCount = value;
    portEXIT_CRITICAL(&timerMux);
  }
}
